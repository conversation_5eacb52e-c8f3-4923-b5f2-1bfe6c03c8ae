仅使用后面批次的新数据训练模型

第 1/3 折交叉验证
D:\ProgramData\Anaconda3\Lib\site-packages\torch_geometric\deprecation.py:26: UserWarning: 'data.DataLoader' is deprecated, use 'loader.DataLoader' instead
  warnings.warn(out)
Epoch: 010, Loss: 4.0191, Train Acc: 0.5725, Val Acc: 0.5703, Val F1: 0.3632
Epoch: 020, Loss: 1.0621, Train Acc: 0.6196, Val Acc: 0.5938, Val F1: 0.5206
Epoch: 030, Loss: 1.0773, Train Acc: 0.6196, Val Acc: 0.5859, Val F1: 0.5148
Epoch: 040, Loss: 0.8280, Train Acc: 0.6353, Val Acc: 0.6172, Val F1: 0.5047
Early stopping at epoch 45
D:\Pythonpro1\adni_yuchuli_hou\adni_yuchuli_hou_02\train_gcn_model.py:305: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  model.load_state_dict(torch.load(f'best_model_fold{fold+1}.pt'))
测试集准确率: 0.6328, F1分数: 0.5338

第 2/3 折交叉验证
D:\ProgramData\Anaconda3\Lib\site-packages\torch_geometric\deprecation.py:26: UserWarning: 'data.DataLoader' is deprecated, use 'loader.DataLoader' instead
  warnings.warn(out)
Epoch: 010, Loss: 1.8053, Train Acc: 0.6000, Val Acc: 0.6172, Val F1: 0.5632
Epoch: 020, Loss: 1.3401, Train Acc: 0.6235, Val Acc: 0.6094, Val F1: 0.4994
Early stopping at epoch 21
D:\Pythonpro1\adni_yuchuli_hou\adni_yuchuli_hou_02\train_gcn_model.py:305: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  model.load_state_dict(torch.load(f'best_model_fold{fold+1}.pt'))
测试集准确率: 0.6406, F1分数: 0.6059

第 3/3 折交叉验证
D:\ProgramData\Anaconda3\Lib\site-packages\torch_geometric\deprecation.py:26: UserWarning: 'data.DataLoader' is deprecated, use 'loader.DataLoader' instead
  warnings.warn(out)
Epoch: 010, Loss: 3.0317, Train Acc: 0.5703, Val Acc: 0.5748, Val F1: 0.3650
Epoch: 020, Loss: 1.3553, Train Acc: 0.6055, Val Acc: 0.6299, Val F1: 0.5554
Epoch: 030, Loss: 0.7579, Train Acc: 0.6172, Val Acc: 0.6457, Val F1: 0.5601
Epoch: 040, Loss: 1.1402, Train Acc: 0.6016, Val Acc: 0.6142, Val F1: 0.5434
Early stopping at epoch 48
D:\Pythonpro1\adni_yuchuli_hou\adni_yuchuli_hou_02\train_gcn_model.py:305: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  model.load_state_dict(torch.load(f'best_model_fold{fold+1}.pt'))
测试集准确率: 0.6535, F1分数: 0.5661

总体结果:
平均准确率: 0.6423 ± 0.0085
平均F1分数: 0.5686 ± 0.0295
每个被试的预测结果已保存到 subject_predictions.csv